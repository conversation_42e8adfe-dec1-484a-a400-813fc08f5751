<script lang="ts" setup>
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import {
  ElMessageBox,
  ElNotification

} from 'element-plus';
import { sysRoleSave } from '#/api/sys/role';
const isUpdate = ref(false);

const [AddForm, formApi] = useVbenForm({
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-2',
  showDefaultActions: false,
  schema: [
    {
      label: '角色代码',
      component: 'Input',
      componentProps: {
        placeholder: '角色代码',
        allowClear: true,
      },
      help: '请保证唯一',
      rules: 'required',
      fieldName: "roleCode",
      dependencies: {
        disabled() {
          return isUpdate.value;
        },
        triggerFields: ['isUpdate'] // 添加触发字段
      },
    },
    {
      label: "角色名称",
      fieldName: "roleName",
      component: "Input",
      componentProps: {
        placeholder: "角色名称"
      },
      rules: 'required',
    },
    {
      label: "描述",
      fieldName: "description",
      component: "Input",
      componentProps: {
        placeholder: "描述"
      },

    },
    {
      label: "备注",
      fieldName: "remark",
      component: "Input",
    },

  ]
})
const record = ref();
const [Modal, modalApi] = useVbenModal({
  closeOnClickModal: false,
  draggable: true,
  onOpenChange(isOpen) {
    if (isOpen) {
      record.value = isOpen ? modalApi.getData()?.record || {} : {};
      isUpdate.value = modalApi.getData()?.pageType === 'edit';
      if (!isUpdate.value) {
        formApi.setValues({
        });
      } else {
        formApi.setValues(record.value);
      }
    }


  },
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    ElMessageBox.confirm(
      '确定要保存当前输入角色吗?',
      'Warning',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(() => {
        formApi.validate().then(async (e: any) => {
          if (e.valid) {
            const values = await formApi.getValues();
            modalApi.setState({ confirmLoading: true });
            await sysRoleSave(values);
            ElNotification({ duration: 2500, message: '保存成功', type: 'success' });
            modalApi.setState({ confirmLoading: false });
          }
        }).finally(() => {
          modalApi.setState({ confirmLoading: false });;

        })
      })
      .catch(() => {
        ElNotification({ duration: 2500, message: '取消成功', type: 'info' });
      });
  }
});
</script>
<template>
  <Modal append-to-main class="w-[900px]" title="角色新增" title-tooltip="请保证角色代码唯一！">

    <div>
      <AddForm />
    </div>


  </Modal>
</template>

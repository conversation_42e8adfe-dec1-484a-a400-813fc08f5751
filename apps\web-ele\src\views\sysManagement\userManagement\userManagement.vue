<script lang="ts" setup>
import { ref } from 'vue';

import { PageCard } from '@vben/common-ui';

import { ElNotification } from 'element-plus';

import { useVbenForm } from '#/adapter/form';
import { setAccountStatus, sysUserGet } from '#/api/sys/user';
import ServerGridComponent from '#/components/ag-grid/ServerGridComponent.vue';
import { TableAction } from '#/components/table-action';

import UserRole from './user-role.vue';
import UserInfoModal from './userCreateOrEdit.vue';

const userRoleRef = ref();
const gridRef = ref();

const columnDefs = [
  {
    headerName: '真实姓名',
    field: 'name',
    filter: 'agTextColumnFilter',
    filterParams: {
      buttons: ['reset', 'apply'],
      defaultOption: 'equals',
      suppressAndOrCondition: true,
    },
  },
  {
    headerName: '账号',
    field: 'account',
    filter: 'agTextColumnFilter',
    filterParams: {
      buttons: ['reset', 'apply'],
      defaultOption: 'equals',
    },
  },
  {
    headerName: '手机号',
    field: 'telephone',
    filter: 'agTextColumnFilter',
    filterParams: {
      buttons: ['reset', 'apply'],
      defaultOption: 'equals',
    },
  },
  {
    headerName: '邮箱',
    field: 'email',
    filter: 'agTextColumnFilter',
    filterParams: {
      buttons: ['reset', 'apply'],
      defaultOption: 'equals',
    },
  },
  {
    headerName: '状态',
    field: 'status',
    filter: 'agSetColumnFilter',
    filterParams: {
      buttons: ['reset', 'apply'],
      values: ['0', '1'],
      valueFormatter: (params: any) => {
        return params.value === '1' || params.value === 1 ? '启用' : '未启用';
      },
    },
    cellRenderer: (params: any) => {
      const icon = params.value === 1 || params.value === '1' ? '✅' : '❌';
      return icon;
    },
  },
  {
    headerName: '部门',
    field: 'organizationName',
    filter: 'agTextColumnFilter',
    filterParams: {
      buttons: ['reset', 'apply'],
      defaultOption: 'equals',
    },
  },
  {
    headerName: ' 备注',
    field: 'remark',
    filter: 'agTextColumnFilter',
    filterParams: {
      buttons: ['reset', 'apply'],
      defaultOption: 'equals',
    },
  },
  {
    headerName: '操作',
    field: 'action',
    pinned: 'right' as const,
    cellRenderer: 'actionCell',
    width: 500,
    cellRendererParams: {
      actions: [
        {
          label: '编辑',
          callback: async (data: any) => {
            await openEditModal(data);
          },
          auth: ['user.edit'],
          type: 'primary',
          size: 'small',
        },
        {
          label: (params: any) =>
            params.data.status === 0 || params.data.status === '0'
              ? '启用'
              : '禁用',
          callback: async (data: any) => {
            await setAccountStatus({ id: data.id, status: data.status });
            ElNotification({
              duration: 2500,
              message: '执行成功！',
              type: 'success',
            });
            gridRef.value?.handleRefresh();
          },
          auth: ['user.delete'],
          type: (params: any) =>
            params.data.status === 0 || params.data.status === '0'
              ? 'success'
              : 'danger',
          size: 'small',
        },
        {
          label: '授权角色',
          callback: async (data: any) => {
            handleUserRole(data);
          },
          auth: ['user.role'],
          type: 'default',
          size: 'small',
        },
      ],
    },
  },
];
const openUserApi = ref();

// ServerGridComponent 配置
const defaultColDef = {
  flex: 1,
};
const [QueryForm] = useVbenForm({
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  collapsed: false,
  handleSubmit: (values) => {
    gridRef.value?.search(values);
  },
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  schema: [
    {
      label: '账号',
      component: 'Input',
      fieldName: 'account',
    },
    { label: '真实姓名', component: 'Input', fieldName: 'name' },
    { label: '手机号', component: 'Input', fieldName: 'telephone' },
    { label: '邮箱', component: 'Input', fieldName: 'email' },
    {
      label: '状态',
      component: 'Select',
      fieldName: 'status',
      componentProps: {
        clearable: true,
        filterOption: true,
        options: [
          {
            label: '未启用',
            value: '0',
          },
          {
            label: '启用',
            value: '1',
          },
        ],
      },
    },
  ],
});

const openInContentModal = () => {
  openUserApi.value.setData({
    isUpdate: false,
    record: {},
  });
  openUserApi.value.open();
  openUserApi.value.onClosed = () => {
    gridRef.value?.handleRefresh();
  };
};

function openEditModal(data: any) {
  openUserApi.value.setData({
    isUpdate: true,
    record: data,
  });
  openUserApi.value.open();
  openUserApi.value.onClosed = () => {
    gridRef.value?.handleRefresh();
  };
}

// 授权角色
const handleUserRole = (record: any) => {
  userRoleRef.value.setData({
    record,
  });
  userRoleRef.value.open();
};

function handleDataLoaded(_data: any) {
  gridRef.value.gridApi.sizeColumnsToFit();
}
</script>
<template>
  <PageCard auto-content-height>
    <QueryForm />
    <TableAction
      :actions="[
        {
          label: $t('production.Add'),
          type: 'primary',
          icon: 'ep:plus',
          auth: ['user.add'],
          onClick: openInContentModal.bind(null),
        },
      ]"
    />

    <ServerGridComponent
      ref="gridRef"
      :api-function="sysUserGet"
      :column-defs="columnDefs"
      :default-col-def="defaultColDef"
      @data-loaded="handleDataLoaded"
    />

    <UserInfoModal ref="openUserApi" />
    <UserRole ref="userRoleRef" />
  </PageCard>
</template>

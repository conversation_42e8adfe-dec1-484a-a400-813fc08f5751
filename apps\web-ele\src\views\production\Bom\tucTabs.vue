<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElTabPane as TabPane, ElTabs as Tabs } from 'element-plus';

import { $t } from '#/locales';

import tucInfo from './tucInfo.vue';
import tucMCode from './tucMCode.vue';

const record = ref();
const isUpdate = ref(false);
const isView = ref(false);

const updateRecord = (newRecord: any) => {
  record.value = newRecord; // 更新 record 的值
};

const activeTab = ref('1');
const handleChange = (key: any) => {
  if (key === '2') {
    // todo 获取数据
  }
};

const [Modal, modalApi] = useVbenModal({
  closeOnClickModal: false,
  // 隐藏确认按钮
  showConfirmButton: false,
  draggable: true,
  loading: true,
  onOpenChange(isOpen) {
    if (isOpen) {
      record.value = modalApi.getData()?.record || {};
      isUpdate.value = modalApi.getData()?.pageType === 'edit';
      isView.value = modalApi.getData()?.pageType === 'view';
      modalApi.setState({ loading: false });
    }
  },
  onCancel() {
    modalApi.close();
  },
});

defineExpose(modalApi);
</script>
<template>
  <Modal class="w-[70%]" title="成品">
    <div class="h-[70%] min-h-[500px]">
      <Tabs v-model="activeTab" @change="handleChange" class="h-[100%]">
        <TabPane :label="$t('production.ProductInfo')" name="1" class="flex-1">
          <tucInfo
            :id="record.id"
            :is-update="isUpdate"
            :is-view="isView"
            :update-record="updateRecord"
          />
        </TabPane>
        <TabPane
          :label="$t('production.MCodeInfo')"
          name="2"
          style="height: 500px"
        >
          <tucMCode :fg-code="record.fgCode" :is-view="isView" />
        </TabPane>
      </Tabs>
    </div>
  </Modal>
</template>

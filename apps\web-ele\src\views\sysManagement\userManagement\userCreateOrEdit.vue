<script lang="ts" setup>
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import {
  ElMessageBox,
  ElNotification
  
} from 'element-plus';
import {  sysUserSave } from '#/api/sys/user';
import { getOrgTreeList } from '#/api/sys/organization'
const [AddForm,formApi] = useVbenForm({
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-2',
  showDefaultActions: false,
  schema: [
  {
      fieldName: 'id',
      label: '用户ID',
      component: 'Input',
      dependencies: {
        show: false,
        triggerFields: ['id'],
      },
    },
    {
      //field: 'username',
      label: '用户名',
      component: 'Input',
      componentProps: {
        placeholder: '请输入用户名',
        allowClear: true,
      },
      dependencies:{
        disabled() {
          return isUpdate.value;
        },
        triggerFields: ['isUpdate'] // 添加触发字段
      },
      rules: 'required',
      fieldName:"account",
    },
    {
      label:"真实姓名",
      fieldName:"name",
      component:"Input",
      componentProps:{
        placeholder:"请输入真实姓名"
      },
      rules: 'required',
    },
    {
      label:"邮箱",
      fieldName:"email",
      component:"Input",
      componentProps:{
        placeholder:"请输入邮箱"
      },
      
    },
    {
      label:"手机号",
      fieldName:"telephone",
      component:"Input",
      componentProps:{
        placeholder:"请输入手机号"
      }
    },
    {
      fieldName: 'organizationId',
      label: '所属上级',
      component: 'ApiTreeSelect',
      componentProps: {
        placeholder: '请选择所属组织',
        clearable: true,
        api: getOrgTreeList,
        labelField: 'name',
        valueField: 'id',
        childrenField: 'children',

      },
    },
    {
      label:"备注",
      fieldName:"remark",
      component:"Input",
    },

  ]
})
const record = ref();
const isUpdate = ref(false);


const [Modal, modalApi] = useVbenModal({
  closeOnClickModal:false,
  draggable: true,
  onOpenChange(isOpen) {
    if(isOpen)
    {
      record.value = modalApi.getData()?.record || {}  ;
      isUpdate.value = modalApi.getData()?.isUpdate || false ;
    }
    
    formApi.setValues(record.value);

  },
  onCancel() {
    modalApi.close();
  },
  onConfirm(){
    ElMessageBox.confirm(
    '确定要保存当前选择用户吗?',
    'Warning',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      formApi.validate().then(async (e: any) => {
      if (e.valid) {
        const values = await formApi.getValues();
        modalApi.setState({ confirmLoading: true });
        await sysUserSave(values);
        ElNotification({duration: 2500,message: '保存成功',type: 'success'});
        //modalApi.setState({confirmLoading: false });
        modalApi.close();
      }
    }).finally(() => {
      modalApi.setState({ confirmLoading: false });;
      
    }) 
    })
    .catch(() => {
      ElNotification({duration: 2500,message: '取消成功',type: 'info'});
    });
  }
});
defineExpose(modalApi);
</script>
<template>
  <Modal
    append-to-main
    class="w-[900px]"
    title="用户新增"
    title-tooltip="用户新增"
    description="创建用户，用于登录系统"
  >

    <div>
      <AddForm />
    </div>

    
  </Modal>
</template>

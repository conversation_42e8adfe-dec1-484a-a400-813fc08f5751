<script lang="ts" setup>
import { ref, onMounted } from 'vue';

import { $t } from '#/locales';

import { ElMessage } from "element-plus";
import { useVbenForm } from '#/adapter/form';
import { PageCard } from '@vben/common-ui';

// import { IconifyIcon } from '@vben/icons';
import { getTUCFPList, deleteTucfpInfo, auditBom, releaseBom, cancelReleaseBom } from '#/api/production/bom'
import { TableAction } from '#/components/table-action';

import ClientGridComponent from '#/components/ag-grid/ClientGridComponent.vue';
import importMCode from './import.vue';

import tucTabs from './tucTabs.vue';
import { useMessage } from '#/components/elementPlus/useMessage';
const gridRef = ref<InstanceType<typeof ClientGridComponent> | null>(null);
const tucTabsRef = ref();
const importMCodeRef = ref();

async function getData(data: any | [] = []) {
  const res = await getTUCFPList({
    ...data
  });
  if (Array.isArray(res)) {
    // 确保 res 是数组类型
    rowData.value = res;
  }

}


const columnDefs: any[] = [
  { headerName: $t('production.ItemCode'), field: 'fgCode', width: 135 },
  {
    headerName: $t('production.NowStatus'),
    field: 'status',
    cellRenderer: (params: any) => {
      if (params.value === 'new') {
        return $t('production.New');
      }
      if (params.value === 'audit') {
        return $t('production.Auditing');
      }
      if (params.value === 'release') {
        return $t('production.Release');
      }
      if (params.value === 'cancel') {
        return $t('production.CancelRelease');
      }
      return params.value;
    }, width: 135
  },
  { headerName: $t('production.Seat'), field: 'seat', width: 115 },
  { headerName: $t('production.SeriesNo'), field: 'seriesNo', width: 125 },
  { headerName: $t('production.StyleNo'), field: 'styleNo', width: 115 },
  { headerName: $t('production.VersionNo'), field: 'versionNo', width: 125 },
  { headerName: $t('production.TargetMarket'), field: 'targetMarket', width: 125 },
  { headerName: $t('production.UnitWeightKg'), field: 'unitWeightKg', width: 155 },
  { headerName: $t('production.ProductNature'), field: 'productNature', width: 135 },
  { headerName: $t('production.StorageMethod'), field: 'storageMethod', width: 135 },
  { headerName: $t('production.StorageCount'), field: 'storageCount', width: 125 },
  { headerName: $t('production.BoxWeight'), field: 'boxWeight', width: 135 },
  { headerName: $t('production.Updater'), field: 'updateUserName', width: 125 },
  { headerName: $t('production.UpdateDate'), field: 'updateTime', width: 125 },
  { headerName: $t('production.Reviewer'), field: 'reviewer', width: 125 },
  { headerName: $t('production.ReviewDate'), field: 'reviewDate', width: 135 },
  { headerName: $t('production.Publisher'), field: 'publisher', width: 135 },
  { headerName: $t('production.PublishDate'), field: 'publishDate', width: 135 },
  { headerName: $t('production.Valid'), field: 'valid', width: 115 },
  { headerName: $t('production.PurchaseNotice'), field: 'purchaseNotice', width: 135 },
  { headerName: $t('production.ProductionNotice'), field: 'productionNotice', width: 135 },
  { headerName: $t('production.StorageTransportNotice'), field: 'storageTransportNotice', width: 135 },
  { headerName: $t('production.CancelFlag'), field: 'cancelFlag', width: 135 },
  { headerName: $t('production.DomesticExport'), field: 'domesticExport', width: 115 },
  { headerName: $t('production.UnitPrice'), field: 'unitPrice', width: 115 },
  { headerName: $t('production.UnitPriceUpdater'), field: 'unitPriceUpdater', width: 155 },
  { headerName: $t('production.UnitPriceUpdateDate'), field: 'unitPriceUpdateDate', width: 155 },
  { headerName: $t('production.Remark'), field: 'remark', width: 115 },
  { headerName: $t('production.FGItemNo'), field: 'fgItemNo', width: 135 },
  { headerName: $t('production.ProdCode'), field: 'productCode', width: 135 },
  { headerName: $t('production.ProdName'), field: 'productName', width: 135 },
  { headerName: $t('production.SpecModel'), field: 'specModel', width: 115 },
  { headerName: $t('production.Unit'), field: 'unit', width: 115 },
  { headerName: $t('production.SalesCountry'), field: 'salesCountry', width: 125 },
  {
    headerName: $t('production.Action'),
    field: "action", // 注意这里field不是实际的数据字段，仅用于标识
    pinned: 'right',
    cellRenderer: 'actionCell',
    flex: 1,
    cellRendererParams: {
      actions: [
        {
          label: $t('production.Edit'),
          callback: (data: any) => {
            handleEdit(data, 'edit')
          },
          auth: ['tucfp.edit'],
          type: 'primary',
          size: 'small',
          disabled: (params: any) => params.data.status !== 'new' && params.data.status !== 'cancel', // Disable if status is not 'new' or 'cancel'
          //eventName: 'editEvent' // 自定义事件名
        },
        {
          label: $t('basic.view'),
          callback: (data: any) => {
            handleEdit(data, 'view')
          },
          auth: ['tucfp.view'],
          type: 'primary',
          size: 'small',
          //eventName: 'editEvent' // 自定义事件名
        },
        {
          label: $t('production.Delete'),
          callback: (data: any) => {
            handleDelete(data)
          },
          auth: ['tucfp.delete'],
          type: 'danger',
          size: 'small',
          disabled: (params: any) => params.data.status !== 'new', // Disable if status is not 'new'
        },
      ]
    }
  },

];
const defaultColDef: any = {
  //flex: 1

};
const rowData = ref<any[]>([])


const [QueryForm] = useVbenForm({
  handleSubmit: (values) => {
    getData(values);
  },
  submitButtonOptions: {
    content: $t('page.base.search'),
  },
  resetButtonOptions: {
    content: $t('page.base.reset'),
  },
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  collapsed: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  schema: [
    {
      label: $t('production.Seat'),
      component: 'Input',
      fieldName: 'seat',
    },
    {
      label: $t('production.SeriesNo'),
      component: 'Input',
      fieldName: 'seriesNo',
    },
    {
      label: $t('production.Remark'),
      component: 'Input',
      fieldName: 'remark',
    },
  ]
});

const handleAdd = (data: any) => {
  //tucCreateOrEditRef.value.setData({
  //   record: data,
  //  isUpdate: false,
  // });
  // tucCreateOrEditRef.value.open();
  //tucCreateOrEditRef.value.onClosed = () => {
  // 调用刷新数据的方法
  // getData();
  //};

  tucTabsRef.value.setData({
    record: data,
    isUpdate: false,
  });
  tucTabsRef.value.open();
  tucTabsRef.value.onClosed = () => {
    // 调用刷新数据的方法
    getData();
  };

}
const handleAudit = async (data: any) => {
  //判断是否勾选数据
  const gridRows = gridRef.value?.gridApi?.getSelectedRows();
  //如果没有勾选数据，则提示
  if (!gridRows || gridRows.length === 0) {
    useMessage().showMessage('warning', '请勾选数据');
    return;
  }
  const invalidRows = gridRows.filter(row => row.status !== 'audit' );
  if (invalidRows.length > 0) {
    useMessage().showMessage('warning', $t('production.AuditisR'));
    return;
  }
  // 获取所有选中行的fgCode并用逗号拼接
  const fgCodes = gridRows.map(row => row.fgCode).join(',');
  //进行二次确认
  useMessage().showMessageBox('confirm', '确定要审核吗?', '提示', 'warning', async (action) => {
    if (action === 'confirm') {
      //获取到勾选的数据
      await auditBom({
        fgCodes: fgCodes
      });
      getData();
      useMessage().showMessage('success', $t('production.AuditSuccess'));
    }
  });

}
const handleReleaseCancel = async (data: any) => {
  //判断是否勾选数据
  const gridRows = gridRef.value?.gridApi?.getSelectedRows();
  //如果没有勾选数据，则提示
  if (!gridRows || gridRows.length === 0) {
    useMessage().showMessage('warning', '请勾选数据');
    return;
  }
  // Check if any selected rows have invalid status
  const invalidRows = gridRows.filter(row => row.status == 'new' || row.status == 'cancel');
  if (invalidRows.length > 0) {
    useMessage().showMessage('warning', $t('production.AuditisR'));
    return;
  }
  // 获取所有选中行的fgCode并用逗号拼接
  const fgCodes = gridRows.map(row => row.fgCode).join(',');
  //进行二次确认
  useMessage().showMessageBox('confirm', '确定要取消发布吗?', '提示', 'warning', async (action) => {
    if (action === 'confirm') {
      //调用取消接口
      await cancelReleaseBom({
        fgCodes: fgCodes
      });
      useMessage().showMessage('success', $t('production.CancelReleaseSuccess'));
      getData();

    }
  });

}
const handleRelease = async (data: any) => {
  //判断是否勾选数据
  const gridRows = gridRef.value?.gridApi?.getSelectedRows();
  //如果没有勾选数据，则提示
  if (!gridRows || gridRows.length === 0) {
    useMessage().showMessage('warning', '请勾选数据');
    return;
  }
 
  const invalidRows = gridRows.filter(row => row.status !== 'new' && row.status !== 'cancel');
  if (invalidRows.length > 0) {
    useMessage().showMessage('warning', $t('production.NewOrCancelisR'));
    return;
  }
 // 获取所有选中行的fgCode并用逗号拼接
 const fgCodes = gridRows.map(row => row.fgCode).join(',');
  //进行二次确认
  useMessage().showMessageBox('confirm', '确定要发布吗?', '提示', 'warning', async (action) => {
    if (action === 'confirm') {
      //获取到勾选的数据
      await releaseBom({
        fgCodes: fgCodes
      });
      useMessage().showMessage('success', $t('production.WaitAudit'));
      getData();

    }
  });
}
const rowSelection = ref({
  mode: "multiRow" as "multiRow",  // singleRow 默认单选//multiRow
  checkboxes: true,//默认关闭
  headerCheckbox: true, //默认关闭
  copySelectedRows: true,
});
const handleEdit = (data: any, pageType: String) => {
  tucTabsRef.value.setData({
    record: data,
    pageType: pageType,
  });
  tucTabsRef.value.open();
  tucTabsRef.value.onClosed = () => {
    // 调用刷新数据的方法
    getData();
  };

}
const handleImport = () => {

  importMCodeRef.value.open();
  importMCodeRef.value.onClosed = () => {
    //调用刷新数据的方法
    getData();
  };

}

//删除
const handleDelete = async (data: any) => {
  try {
    await deleteTucfpInfo({ id: data.id }); // 假设 data.id 是要删除的记录的 ID
    // 这里可以添加一个提示框，使用 Element Plus 的 Message 组件
    ElMessage.success($t('production.DeleteSuccess')); // 提示删除成功
    getData(); // 重新获取数据以更新表格
  } catch (error) {

  }
}

onMounted(() => {
  getData();
});
</script>

<template>
  <PageCard auto-content-height>


    <QueryForm />
    <TableAction :actions="[
      {
        label: $t('production.Add'),
        type: 'primary',
        icon: 'ep:plus',
        auth: ['tucfp.add'],
        onClick: handleAdd.bind(null),
      },
      {
        label: $t('production.Import'),
        type: 'primary',
        icon: 'ep:upload',
        auth: ['tucfp.import'],
        onClick: handleImport.bind(null),
      },
      {
        label: $t('production.Release'),
        type: 'primary',
        icon: 'ep:circle-plus-filled',
        auth: ['tucfp.release'],
        onClick: handleRelease.bind(null),
      },
      {
        label: $t('production.Audit'),
        type: 'primary',
        icon: 'ep:check',
        auth: ['tucfp.audit'],
        onClick: handleAudit.bind(null),
      },
      {
        label: $t('production.ReleaseCancel'),
        type: 'primary',
        icon: 'ep:circle-close',
        auth: ['tucfp.releaseCancel'],
        onClick: handleReleaseCancel.bind(null),
      },

    ]">
    </TableAction>

    <ClientGridComponent ref="gridRef" :columnDefs="columnDefs" :rowData="rowData" :pageSize="20"
      :defaultColDef="defaultColDef" :rowSelection="rowSelection" />
    <!-- <tucCreateOrEdit ref="tucCreateOrEditRef" /> -->
    <tucTabs ref="tucTabsRef" />
    <importMCode ref="importMCodeRef" />
  </PageCard>
</template>

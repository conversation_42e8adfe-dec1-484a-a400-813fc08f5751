<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import { PageCard } from '@vben/common-ui';
import { downloadFileFromBase64 } from '@vben/utils';

import { ElNotification } from 'element-plus';

import {
  cancelConfirmPurchaseOrder,
  confirmPurchaseOrder,
  deletePurchase,
  downLoadCustomsExcel,
  GetPurchaseOrderList,
} from '#/api/purchase/imported';
import ServerGridComponent from '#/components/ag-grid/ServerGridComponent.vue';
import { useMessage } from '#/components/elementPlus/useMessage';
import { TableAction } from '#/components/table-action';
import { $t } from '#/locales';

import ImportedDetail from './components/ImportedDetail.vue';
import ImportImported from './components/ImportImported.vue';
import QueryForm from './components/QueryForm.vue';

/**
 * 组件引用
 * importedDetailRef: 详情弹窗组件引用
 * importImportedRef: 导入弹窗组件引用
 * gridRef: 表格组件引用
 */
const importedDetailRef = ref();
const importImportedRef = ref();
const gridRef = ref<any>(null);

/**
 * 数据状态
 * searchParams: 搜索参数
 */
const searchParams = ref({});

/**
 * 消息提示组合函数
 */
const { showConfirm } = useMessage();

/**
 * 表格列定义
 * 定义表格的列结构，包括列标题、字段名、排序和筛选等配置
 */
const columnDefs: any[] = [
  {
    headerName: $t('purchase.importOrderNo'),
    field: 'orderNo',
    sortable: true, // 启用排序
  },
  {
    headerName: $t('purchase.orderDate'),
    field: 'orderDate',
    filterType: 'date', // 使用 filterType 避免与 AG-Grid 的 type 字段冲突
    sortable: true, // 启用排序
  },
  {
    headerName: $t('purchase.orderStatus'),
    field: 'orderStatus',
    sortable: true, // 启用排序
    valueFormatter: (params: any) => {
      const statusMap: Record<string, string> = {
        new: $t('purchase.new'),
        confirm: $t('purchase.confirm'),
        store: $t('purchase.store'),
      };
      return statusMap[params.value] || params.value;
    },
  },
  {
    headerName: $t('purchase.poNo'),
    field: 'poNo',
    sortable: true, // 启用排序
  },
  {
    headerName: $t('purchase.declarationNo'),
    field: 'declarationNo',
    sortable: true, // 启用排序
  },
  {
    headerName: $t('purchase.supplier'),
    field: 'supplier',
    sortable: true, // 启用排序
  },
  {
    headerName: $t('purchase.receiptDate'),
    field: 'receiptDate',
    sortable: true, // 启用排序
  },
  {
    headerName: $t('purchase.confirmUserName'),
    field: 'confirmUserName',
    sortable: true, // 启用排序
  },
  {
    headerName: $t('purchase.confirmDateTime'),
    field: 'confirmDateTime',
    sortable: true, // 启用排序
  },
  {
    headerName: $t('production.Action'),
    field: 'action', // 注意这里field不是实际的数据字段，仅用于标识
    pinned: 'right',
    cellRenderer: 'actionCell',
    // flex: 1,
    cellRendererParams: {
      actions: {
        label: '...',
        primaryActions: [
          {
            label: $t('basic.view'),
            callback: (data: any) => {
              handleEdit(data, 'view');
            },
            auth: ['purchase.imported.view'],
            type: 'primary',
            size: 'small',
          },
          {
            label: $t('purchase.confirmOrder'),
            callback: (data: any) => {
              handleConfirmOrder(data);
            },
            auth: ['purchase.imported.confirm'],
            type: 'success',
            size: 'small',
            show: (data: any) => data.orderStatus === 'new', // 只有新建状态才显示确认按钮
          },
          {
            label: $t('purchase.cancelConfirm'),
            callback: (data: any) => {
              handleCancelConfirm(data);
            },
            auth: ['purchase.imported.cancelConfirm'],
            type: 'warning',
            size: 'small',
            show: (data: any) => data.orderStatus === 'confirm', // 只有确认状态才显示取消确认按钮
          },
        ],
        menuItems: [
          {
            label: $t('purchase.exportCustomsData'),
            callback: (data: any) => {
              handleCustomsExcel(data);
            },
            auth: ['purchase.imported.exportCustoms'],
            type: 'danger',
            size: 'small',
          },
          {
            label: $t('basic.delete'),
            callback: (data: any) => {
              handleDelete(data);
            },
            auth: ['purchase.imported.delete'],
            type: 'danger',
            size: 'small',
          },
        ],
      },
    },
  },
];

/**
 * 搜索参数处理函数
 * 为 ServerGridComponent 添加固定的 orderType 参数
 * 这个函数在每次 API 调用时都会被调用，包括初始化时
 */
const searchParamsProcessor = (params: any) => {
  return {
    ...params, // 传入的搜索参数
    orderType: 'import', // 固定参数，确保总是传递
  };
};

/**
 * 处理编辑或查看操作
 * 打开详情弹窗，根据pageType设置为编辑或查看模式
 * @param data 当前行数据
 * @param pageType 页面类型，'edit'为编辑，'view'为查看
 */
const handleEdit = (data: any, pageType: string) => {
  importedDetailRef.value.modalApi.setData({
    record: data,
    isUpdate: pageType === 'edit',
    isView: pageType === 'view',
  });
  importedDetailRef.value.modalApi.open();
  // 关闭后刷新数据
  importedDetailRef.value.modalApi.setState({
    onClosed: () => {
      gridRef.value?.handleRefresh();
    },
  });
};

/**
 * 处理删除操作
 * 弹出确认框，确认后删除数据
 * @param data 要删除的数据行
 */
const handleDelete = async (data: any) => {
  try {
    // 弹出确认对话框 - 使用防抖动的确认对话框
    await showConfirm($t('basic.confirmDelete'), $t('basic.tips'), {
      confirmButtonText: $t('basic.confirm'),
      cancelButtonText: $t('basic.cancel'),
      type: 'warning',
    });

    // 调用删除API
    await deletePurchase({ id: data.id });
    ElNotification({
      duration: 2500,
      message: $t('basic.deleteSuccess'),
      type: 'success',
    });
    gridRef.value?.handleRefresh(); // 删除成功后刷新数据
  } catch (error) {
    console.error($t('basic.deleteFailed'), error);
  }
};

/**
 * 报关单数据导出
 * 弹出确认框，确认后导出
 * @param data 要导出的对应的报关单数据
 */
const handleCustomsExcel = async (data: any) => {
  // 弹出确认对话框 - 使用防抖动的确认对话框
  await showConfirm(
    $t('purchase.exportCustomsDataTip', { orderNo: data.orderNo }),
    $t('basic.tips'),
    {
      confirmButtonText: $t('basic.confirm'),
      cancelButtonText: $t('basic.cancel'),
      type: 'warning',
    },
  );

  const customsExcel = await downLoadCustomsExcel({ orderNo: data.orderNo });

  // 使用后端返回的mimeType创建完整的Data URL
  const dataUrl = `data:${customsExcel.mimeType};base64,${customsExcel.fileContent}`;

  downloadFileFromBase64({
    fileName: customsExcel.fileName,
    source: dataUrl,
  });
  useMessage().showMessage('success', $t('basic.downloadSuccess'));
};

/**
 * 处理导入操作
 * 打开导入弹窗
 */
const handleImport = () => {
  importImportedRef.value.modalApi.open();
  // 关闭后刷新数据
  importImportedRef.value.modalApi.setState({
    onClosed: () => {
      gridRef.value?.handleRefresh();
    },
  });
};

/**
 * 处理确认订单操作
 * 将订单状态从"新建"更改为"确认"
 * @param data 要确认的订单数据
 */
const handleConfirmOrder = async (data: any) => {
  try {
    // 弹出确认对话框 - 使用防抖动的确认对话框
    await showConfirm($t('purchase.confirmOrderTip'), $t('basic.tips'), {
      confirmButtonText: $t('basic.confirm'),
      cancelButtonText: $t('basic.cancel'),
      type: 'warning',
    });

    // 调用确认订单API
    await confirmPurchaseOrder({
      id: data.id,
    });
    ElNotification({
      duration: 2500,
      message: $t('purchase.confirmOrderSuccess'),
      type: 'success',
    });
    gridRef.value?.handleRefresh(); // 确认成功后刷新数据
  } catch (error) {
    if (error !== 'cancel') {
      console.error($t('purchase.confirmOrderFailed'), error);
      ElNotification({
        duration: 2500,
        message: $t('purchase.confirmOrderFailed'),
        type: 'error',
      });
    }
  }
};

/**
 * 处理取消确认订单操作
 * 将订单状态从"确认"更改回"新建"
 * @param data 要取消确认的订单数据
 */
const handleCancelConfirm = async (data: any) => {
  try {
    // 弹出确认对话框 - 使用防抖动的确认对话框
    await showConfirm($t('purchase.cancelConfirmTip'), $t('basic.tips'), {
      confirmButtonText: $t('basic.confirm'),
      cancelButtonText: $t('basic.cancel'),
      type: 'warning',
    });

    // 调用取消确认订单API
    await cancelConfirmPurchaseOrder({ id: data.id });
    ElNotification({
      duration: 2500,
      message: $t('purchase.cancelConfirmSuccess'),
      type: 'success',
    });
    gridRef.value?.handleRefresh(); // 取消确认成功后刷新数据
  } catch (error) {
    if (error !== 'cancel') {
      console.error($t('purchase.cancelConfirmFailed'), error);
      ElNotification({
        duration: 2500,
        message: $t('purchase.cancelConfirmFailed'),
        type: 'error',
      });
    }
  }
};

/**
 * 处理搜索操作
 * 更新搜索参数并重新获取数据
 * @param formData 表单数据
 */
const handleSearch = (formData: any) => {
  searchParams.value = formData;
  gridRef.value?.search(formData);
};

/**
 * 处理重置操作
 * 清空搜索参数并重新获取数据
 */
const handleReset = () => {
  searchParams.value = {};
  gridRef.value?.search({});
};

/**
 * 组件挂载时执行
 * ServerGridComponent 会自动加载数据，无需手动调用
 */
onMounted(() => {
  // ServerGridComponent 会自动加载数据
});
</script>

<template>
  <PageCard auto-content-height>
    <!-- 查询表单组件 -->
    <QueryForm @search="handleSearch" @reset="handleReset" />

    <!-- 操作按钮区域 -->
    <TableAction
      :actions="[
        {
          label: $t('basic.import'),
          type: 'primary',
          icon: 'ep:upload',
          auth: ['purchase.imported.add'],
          onClick: handleImport.bind(null),
        },
      ]"
    />

    <!-- 数据表格 -->
    <ServerGridComponent
      ref="gridRef"
      :column-defs="columnDefs"
      :api-function="GetPurchaseOrderList"
      :search-params-processor="searchParamsProcessor"
      :page-size="10"
    />

    <!-- 详情弹窗组件 -->
    <ImportedDetail ref="importedDetailRef" />

    <!-- 导入弹窗组件 -->
    <ImportImported ref="importImportedRef" />
  </PageCard>
</template>

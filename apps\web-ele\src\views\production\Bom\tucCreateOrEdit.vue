<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElMessageBox, ElNotification } from 'element-plus';

import { useVbenForm } from '#/adapter/form';
import { getTucfpInfo, tucfpAddOrEdit } from '#/api/production/bom';
import { $t } from '#/locales';

const [AddForm, formApi] = useVbenForm({
  showDefaultActions: false,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
      placeholder: '',
    },
  },
  wrapperClass: 'grid-cols-12',
  schema: [
    {
      fieldName: 'id',
      label: '用户ID',
      component: 'Input',
      dependencies: {
        show: false,
        triggerFields: ['id'],
      },
    },
    {
      fieldName: 'fgCode',
      label: $t('production.ItemCode'),
      component: 'Input',
      disabled: true,
      // 占满两列
      formItemClass: 'col-span-3 items-start',
      componentProps: {
        placeholder: null,
      },
    },

    {
      label: $t('production.ProductNature'),
      fieldName: 'productNature',
      component: 'Input',
      rules: 'required',
      formItemClass: 'col-span-3 items-start',
    },
    {
      label: $t('production.Valid'),
      fieldName: 'valid',
      component: 'Checkbox',
      formItemClass: 'col-span-2 items-start',
    },
    {
      label: $t('production.Invalid'),
      fieldName: 'invalid',
      component: 'Checkbox',
      formItemClass: 'col-span-2 items-start',
    },
    {
      label: $t('production.Seat'),
      fieldName: 'seat',
      component: 'Input',
      formItemClass: 'col-span-3',
    },
    {
      label: $t('production.SeriesNo'),
      fieldName: 'seriesNo',
      component: 'Input',
      formItemClass: 'col-span-3',
    },
    {
      label: $t('production.StyleNo'),
      fieldName: 'styleNo',
      component: 'Input',
      formItemClass: 'col-span-3',
    },
    {
      label: $t('production.TargetMarket'),
      fieldName: 'targetMarket',
      component: 'Input',
      formItemClass: 'col-span-3',
    },
    {
      label: $t('production.VersionNo'),
      fieldName: 'versionNo',
      component: 'Input',
      formItemClass: 'col-span-3',
    },
    {
      label: $t('production.UnitWeightKg'),
      fieldName: 'unitWeightKg',
      component: 'Input',
      formItemClass: 'col-start-4 col-span-3',
    },
    {
      label: $t('production.StorageMethod'),
      fieldName: 'storageMethod',
      component: 'Input',
      formItemClass: 'col-span-3',
    },
    {
      label: $t('production.StorageCount'),
      fieldName: 'storageCount',
      component: 'Input',
      formItemClass: 'col-span-3',
    },
    {
      label: $t('production.BoxWeight'),
      fieldName: 'boxWeight',
      component: 'Input',
      formItemClass: 'col-span-3',
    },
    {
      label: $t('production.ProdCode'),
      fieldName: 'productCode',
      component: 'Input',
      formItemClass: 'col-span-3',
    },
    {
      label: $t('production.Unit'),
      fieldName: 'unit',
      component: 'Input',
      formItemClass: 'col-span-2',
    },
    {
      label: $t('production.DomesticExport'),
      fieldName: 'domesticExport',
      component: 'Input',
      formItemClass: 'col-span-3',
    },
    {
      label: $t('production.SalesCountry'),
      fieldName: 'salesCountry',
      component: 'Input',
      formItemClass: 'col-span-3',
    },
    {
      label: $t('production.ProdName'),
      fieldName: 'productName',
      component: 'Input',
      formItemClass: 'col-span-4',
    },
    {
      label: $t('production.SpecModel'),
      fieldName: 'specModel',
      component: 'Input',
      formItemClass: 'col-span-4',
    },
    {
      fieldName: 'remark',
      label: $t('production.Remark'),
      component: 'Input',
      componentProps: {
        type: 'textarea',
      },
      formItemClass: 'col-span-12 items-start',
    },
    {
      label: $t('production.Updater'),
      fieldName: 'updateUserName',
      component: 'Input',
      disabled: true,
      formItemClass: 'col-start-1 col-span-2',
      componentProps: {
        placeholder: null,
      },
    },
    {
      label: $t('production.UpdateDate'),
      fieldName: 'updateTime',
      component: 'Input',
      disabled: true,
      formItemClass: 'col-span-3',
      componentProps: {
        placeholder: null,
      },
    },
    {
      label: $t('production.Reviewer'),
      fieldName: 'reviewer',
      disabled: true,
      component: 'Input',
      formItemClass: 'col-span-3',
      componentProps: {
        placeholder: null,
      },
    },
  ],
});
const record = ref();
const isUpdate = ref(false);
const title = $t('production.Reviewer');

const [Modal, modalApi] = useVbenModal({
  closeOnClickModal: false,
  draggable: true,
  onOpenChange(isOpen) {
    if (isOpen) {
      record.value = modalApi.getData()?.record || {};
      isUpdate.value = modalApi.getData()?.isUpdate || false;
      if (isUpdate.value) {
        getTucfpInfo({ id: record.value.id }).then((res: any) => {
          formApi.setValues(res);
        });
      }
    }
  },
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    ElMessageBox.confirm('确定要保存当前输入数据吗?', 'Warning', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        formApi
          .validate()
          .then(async (e: any) => {
            if (e.valid) {
              const values = await formApi.getValues();
              modalApi.setState({ confirmLoading: true });
              await tucfpAddOrEdit(values);
              ElNotification({
                duration: 2500,
                message: $t('production.SaveSuccess'),
                type: 'success',
              });
              // modalApi.setState({confirmLoading: false });
              modalApi.close();
            }
          })
          .finally(() => {
            modalApi.setState({ confirmLoading: false });
          });
      })
      .catch(() => {
        ElNotification({
          duration: 2500,
          message: $t('production.CancelSuccess'),
          type: 'info',
        });
      });
  },
});
defineExpose(modalApi);
</script>
<template>
  <Modal append-to-main class="w-[1600px]" :title="title">
    <AddForm />
  </Modal>
</template>

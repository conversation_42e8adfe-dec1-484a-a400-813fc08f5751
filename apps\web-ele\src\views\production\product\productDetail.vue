<script lang="ts" setup>
import { ref, h,computed } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import {
  ElMessageBox,
  ElNotification,
  ElButton as Button
} from 'element-plus';
import { $t } from '#/locales';
import { addOrEditProduct, getProductInfo } from '#/api/production/product';

import declaration from './declaration.vue';

const isUpdate = ref(false);
const isView = ref(false);

const declarationRef = ref();

const [AddForm, formApi] = useVbenForm({
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-12 lg:grid-cols-12',

  commonConfig: {

    // 所有表单项
    componentProps: {
      class: 'w-full',
      placeholder: '',
      readonly: isView,
    },
  },
  layout: 'horizontal',
  collapsed: false,
  schema: [
    {
      fieldName: 'id',
      label: 'ID',
      component: 'Input',
      disabled: true,
      dependencies: {
        triggerFields: ['id'],
        show: false,
      },

    },
    {
      fieldName: 'hsCode',
      label: $t('product.productCode'),
      component: 'Input',
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      rules: 'required',
      dependencies: {
        disabled() {
          return isUpdate.value;
        },
        triggerFields: ['isUpdate'] // 添加触发字段
      },
    },
    {
      fieldName: 'productName',
      label: $t('product.productName'),
      component: 'Input',
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      rules: 'required',
      componentProps: {
      },
    },

    {
      fieldName: 'category',
      label: $t('product.Category'),
      component: 'Input',
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      componentProps: {
      },
    },
    {
      fieldName: 'subCategory',
      label: $t('product.subCategory'),
      component: 'Input',
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      componentProps: {
      },
    },
    {
      fieldName: 'productDec',
      label: $t('product.productDesc'),
      component: 'Input',
      rules: 'required',
      formItemClass: 'col-span-12',
      componentProps: {
        type: 'textarea',
      },
    },
    {
      fieldName: 'btn',
      label: '',
      component: () => {
        return h(
          'div',
          {},
          h(
            Button,
            {
              type: 'primary',
              onClick: getStrDes.bind(null),
              //style: isView ? 'display: none' : ''
            },
            {
              default() {
                return $t('product.details');
              },
            },
          ),
        );
      },
      componentProps: {
      },
      formItemClass: 'col-span-12',
    },
    {
      fieldName: 'meterUnit',
      label: $t('product.productUnit'),
      component: 'Input',
      formItemClass: 'col-start-1 col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      componentProps: {
      },
    },
    {
      fieldName: 'tradUnit',
      label: $t('product.tradingUnit'),
      component: 'Input',
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      componentProps: {
      },
    },
    {
      fieldName: 'remark',
      label: $t('production.Remark'),
      component: 'Input',
      formItemClass: 'col-span-12',
      componentProps: {
        type: 'textarea',
      },
    },

    {
      fieldName: 'createUserName',
      label: $t('basic.createUserName'),
      component: 'Input',
      formItemClass: 'col-start-1 col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      disabled: true,
      componentProps: {
        placeholder: null,
      },
    },
    {
      fieldName: 'createTime',
      label: $t('basic.createTime'),
      component: 'Input',
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      disabled: true,
      componentProps: {
        placeholder: null,
      },
    },
    {
      fieldName: 'updateUserName',
      label: $t('production.HSUpdater'),
      component: 'Input',
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      disabled: true,
      componentProps: {
        placeholder: null,
      },
    },
    {
      fieldName: 'updateTime',
      label: $t('production.HSTimeUpdated'),
      component: 'Input',
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      disabled: true,
      componentProps: {
        placeholder: null,
      },
    },


  ]
})
const record = ref();

const getStrDes = async function () {
  //打开新的窗体，申报要素明细窗体，传入hsCode和商品名称
  const values = await formApi.getValues();
  declarationRef.value.setData({
    hsCode: values.hsCode,
    productName: values.productName
  });

  declarationRef.value.open();
  declarationRef.value.onClosed = () => {
    const selectedData = declarationRef.value.sharedData;
    if (selectedData.payload && selectedData.payload.length > 0) {
      // 更新表单数据
      formApi.setValues({
        productDec: selectedData.payload,
      });

    }


  };
}


const [Modal, modalApi] = useVbenModal({
  closeOnClickModal: false,
  draggable: true,
  onOpenChange(isOpen) {
    if (isOpen) {
      this.loading = true;
      record.value = modalApi.getData()?.record || {};
      isUpdate.value = modalApi.getData()?.pageType === 'edit';
      isView.value = modalApi.getData()?.pageType === 'view';
      modalApi.setState({ showConfirmButton: !isView.value });

      if (isUpdate.value || isView.value) {
        getProductInfo({ id: record.value.id }).then((res: any) => {
          formApi.setValues(res);
        });
      }
      this.loading = false;
    }
  },
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    ElMessageBox.confirm(
      $t('basic.confirmSave'),
      'Warning',
      {
        confirmButtonText: $t('basic.confirm'),
        cancelButtonText: $t('basic.cancel'),
        type: 'warning',
      }
    )
      .then(() => {
        formApi.validate().then(async (e: any) => {
          if (e.valid) {
            const values = await formApi.getValues();
            modalApi.setState({ confirmLoading: true });
            await addOrEditProduct(values);
            ElNotification({ duration: 2500, message: $t('production.SaveSuccess'), type: 'success' });
            //modalApi.setState({confirmLoading: false });
            modalApi.close();
          }
        }).finally(() => {
          modalApi.setState({ confirmLoading: false });;

        })
      })
      .catch(() => {
        ElNotification({ duration: 2500, message: $t('production.CancelSuccess'), type: 'info' });
      });
  }
});
const title = computed(() => {
  if (isView.value) {
    return $t('product.viewDeclaration'); // 查看国外进口
  }
  return isUpdate.value ? $t('purchase.editdEclaration') : $t('product.addDeclaration'); // 编辑国外进口 : 添加国外进口
});
defineExpose(modalApi);
</script>
<template>
  <Modal class="w-[70%]" :title=title>
    <AddForm />
    <declaration ref="declarationRef" />
  </Modal>
</template>

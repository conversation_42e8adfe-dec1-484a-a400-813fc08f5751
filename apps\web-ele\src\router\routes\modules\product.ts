import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:package-variant',
      keepAlive: true,
      order: 500,
      title: $t('product.productManagement'),
      perms: ['product'],
    },
    name: 'Product',
    path: '/Product',
    children: [
      {
        meta: {
          title: $t('product.productManagement'),
          icon: 'mdi:cube-outline',
          // 保存当前页面状态
          keepAlive: true,
          perms: ['product.product'],
          buttons: [
            {
              name: 'product.add',
              meta: {
                icon: 'ic:baseline-add',
                title: $t('basic.add'),
                perms: ['product.product.add'],
              },
            },
            {
              name: 'product.product.edit',
              meta: {
                icon: 'ic:baseline-edit',
                title: $t('basic.edit'),
                perms: ['product.product.edit'],
              },
            },
            {
              name: 'product.product.view',
              meta: {
                icon: 'ic:baseline-remove-red-eye',
                title: $t('basic.view'),
                perms: ['product.product.view'],
              },
            },
            {
              name: 'product.product.delete',
              meta: {
                icon: 'ic:round-delete',
                title: $t('basic.delete'),
                perms: ['product.product.delete'],
              },
            },
          ],

          order: 100,
        },
        name: 'product',
        path: '/Product/product',
        component: () => import('#/views/production/product/product.vue'),
      },
    ],
  },
];

export default routes;

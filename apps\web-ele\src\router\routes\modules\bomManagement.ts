import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:clipboard-check-multiple-outline',
      keepAlive: true,
      order: 500,
      title: $t('production.bomMnagement'),
      perms: ['bomMnagement'],
    },
    name: 'BomMnagement',
    path: '/BomMnagement',
    children: [
      {

        meta: {
          title: $t('production.Material'),
          icon: 'ic:outline-view-compact',
          // 保存当前页面状态
          keepAlive: true,
          perms: ['production.material'],
          buttons: [
            {
              name: "material:add",
              meta: {
                icon: "ic:baseline-add",
                title: $t('production.Add'),
                perms: ['production.material.add'],
              }
            },
            {
              name: "material:release",
              meta: {
                icon: "ic:twotone-rocket-launch",
                title: $t('production.Release'),
                perms: ['production.material.release'],
              }
            },
            {
              name: "material:cancel",
              meta: {
                icon: "ic:baseline-cancel",
                title: $t('production.CancelRelease'),
                perms: ['production.material.cancel'],
              }
            },
            {
              name: "material:edit",
              meta: {
                icon: "ic:baseline-edit",
                title: $t('production.Edit'),
                perms: ['production.material.edit'],
              }
            },
            {
              name: "material:delete",
              meta: {
                icon: "ic:round-delete",
                title: $t('production.Delete'),
                perms: ['production.material.delete'],
              }
            },
            {
              name: "material:view",
              meta: {
                icon: "ic:outline-remove-red-eye",
                title: $t('basic.view'),
                perms: ['production.material.view'],
              }
            },
          ],

          order: 100,
        },
        name: 'material',
        path: '/bomMnagement/material',
        component: () => import('#/views/production/Material/material.vue'),


      },
      {
        meta: {
          title: $t('production.TUC'),
          icon: 'material-symbols-light:broken-image-outline-rounded',
          // 保存当前页面状态
          keepAlive: true,
          perms: ['production.tuc'],
          buttons: [
            {
              name: "tucfp.add",
              meta: {
                icon: "ic:baseline-add",
                title: $t('production.Add'),
                perms: ['tucfp.add'],
              }
            },
            {
              name: "tucfp.edit",
              meta: {
                icon: "ic:baseline-edit",
                title: $t('production.Edit'),
                perms: ['tucfp.edit'],
              }
            },
            {
              name: "tucfp.view",
              meta: {
                icon: "ic:baseline-view",
                title: $t('basic.view'),
                perms: ['tucfp.view'],
              }
            },
            {
              name: "tucfp.delete",
              meta: {
                icon: "ic:round-delete",
                title: $t('production.Delete'),
                perms: ['tucfp.delete'],
              }
            },
            {
              name: "tucfp.import",
              meta: {
                icon: "bi:filetype-xlsx",
                title: $t('production.Import'),
                perms: ['tucfp.import'],
              }
            },
            {
              name: "tucfp.release",
              meta: {
                icon: "ic:twotone-rocket-launch",
                title: $t('production.Release'),
                perms: ['tucfp.release'],
              }
            },
            {
              name: "tucfp.audit",
              meta: {
                icon: "ep:check",
                title: $t('production.Audit'),
                perms: ['tucfp.audit'],
              }
            },
            {
              name: "tucfp.releaseCancel",
              meta: {
                icon: "ep:circle-close",
                title: $t('production.ReleaseCancel'),
                perms: ['tucfp.releaseCancel'],
              }
            },
          ],

          order: 100,
        },
        name: 'tuc',
        path: '/bomMnagement/tuc',
        component: () => import('#/views/production/Bom/tuc.vue'),

      }
    ],
  },
];

export default routes;

<script lang="ts" setup>
import { ref,watch  } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import {
  ElMessageBox,
  ElNotification

} from 'element-plus';
import { sysUserSave } from '#/api/sys/user';

import { getOrgList, getOrgTreeList, addOrganization } from '#/api/sys/organization'

const [AddForm, formApi] = useVbenForm({
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-2',
  showDefaultActions: false,
  schema: [
    {
      fieldName: 'id',
      label: '组织机构ID',
      component: 'Input',
      dependencies: {
        show: false,
        triggerFields: ['id'],
      },
    },
    {
      fieldName: 'parentId',
      label: '所属上级',
      component: 'ApiTreeSelect',
      componentProps: {
        placeholder: '请选择所属上级',
        clearable: true,
        api: getOrgTreeList,
        labelField: 'name',
        valueField: 'id',
        childrenField: 'children',
        allowSelectParent: true  // 如果有类似配置，允许选择父节点
      },
      formItemClass: 'col-span-12',
      
    },
    {
      fieldName: 'name',
      label: '部门名称',
      component: 'Input',
      componentProps: {
        placeholder: '请输入部门名称',
        allowClear: true,
      },
      formItemClass: 'col-span-12',
      rules: 'required',
    },
    {
      label: "负责人",
      fieldName: "leader",
      component: "Input",
      componentProps: {
        placeholder: "请输入负责人"
      },
      formItemClass: 'col-span-12',
    },
    {
      label: "联系电话",
      fieldName: "telephone",
      component: "Input",
      componentProps: {
        placeholder: "请输入联系电话"
      },
      formItemClass: 'col-span-12',
    },
    {
      label: "排序",
      fieldName: "sort",
      component: "InputNumber",
      componentProps: {
        allowClear: true,
        onChange: (value: any) => {
          if (typeof value === 'string') {
            formApi.setFieldValue('sort', Number(value)); // 将输入值转换为数值类型并更新表单字段
          } else {
            formApi.setFieldValue('sort', value);
          }
        },
      },
      formItemClass: 'col-span-12',
    },
    {
      label: "备注",
      fieldName: "remark",
      component: "Input",
      formItemClass: 'col-span-12',
    },

  ]
})
const record = ref();
const isUpdate = ref(false);

// 监听 record 的变化，确保 sort 字段始终是数值类型
watch(record, (newVal : any) => {
  if (newVal && newVal.sort !== undefined) {
    record.value.sort = Number(newVal.sort);
  }
}, { deep: true });

const [Modal, modalApi] = useVbenModal({
  closeOnClickModal: false,
  draggable: true,
  onOpenChange(isOpen) {
    if (isOpen) {
      record.value = modalApi.getData()?.record || {};
      isUpdate.value = modalApi.getData()?.isUpdate || false;
    }

    formApi.setValues(record.value);

  },
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    ElMessageBox.confirm(
      '确定要保存当前输入的信息吗?',
      'Warning',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(() => {
        formApi.validate().then(async (e: any) => {
          if (e.valid) {
            const values = await formApi.getValues();
            modalApi.setState({ confirmLoading: true });
            await addOrganization(values);
            ElNotification({ duration: 2500, message: '保存成功', type: 'success' });

            modalApi.close();
          }
        }).finally(() => {
          modalApi.setState({ confirmLoading: false });;

        })
      })
      .catch(() => {
        ElNotification({ duration: 2500, message: '取消成功', type: 'info' });
      });
  }
});
defineExpose(modalApi);
</script>
<template>
  <Modal append-to-main class="w-[900px]" title="组织机构" title-tooltip="新增组织机构">

    <div>
      <AddForm />
    </div>


  </Modal>
</template>
